#!/usr/bin/env python3
"""
NEXUS Hummingbot Direct Integration Test

Tests the direct Python-level integration between NEXUS and Hummingbot.
This test validates that we can bypass Docker entirely and achieve
superior performance through in-process communication.

Test Coverage:
- Direct Hummingbot strategy instantiation
- Signal processing and trade execution
- Redis bus communication
- Error handling and recovery
- Performance validation
"""

import asyncio
import json
import logging
import time
import pytest
from pathlib import Path
from typing import Dict, Any

# Add project root to path
import sys
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.agents.hummingbot_direct_bridge import HummingbotDirectBridge
from src.agents.phase1_redis_signal_bus import get_unified_signal_bus, SignalType, SignalPriority

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestHummingbotDirectIntegration:
    """Test suite for Hummingbot direct integration"""
    
    def __init__(self):
        self.bridge = None
        self.signal_bus = None
        self.test_results = {}
    
    async def setup(self):
        """Setup test environment"""
        logger.info("🔧 Setting up Hummingbot Direct Integration Test...")
        
        try:
            # Initialize signal bus
            self.signal_bus = get_unified_signal_bus()
            
            # Initialize direct bridge
            self.bridge = HummingbotDirectBridge()
            
            logger.info("✅ Test environment setup complete")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test setup failed: {e}")
            return False
    
    async def test_bridge_initialization(self):
        """Test 1: Bridge Initialization"""
        logger.info("🧪 TEST 1: Bridge Initialization")
        
        try:
            # Test bridge creation
            assert self.bridge is not None, "Bridge should be initialized"
            assert self.bridge.component_name == "hummingbot_direct_bridge", "Component name should be correct"
            assert not self.bridge.is_running, "Bridge should not be running initially"
            
            logger.info("✅ TEST 1 PASSED: Bridge initialization successful")
            self.test_results['bridge_initialization'] = 'PASSED'
            return True
            
        except Exception as e:
            logger.error(f"❌ TEST 1 FAILED: {e}")
            self.test_results['bridge_initialization'] = f'FAILED: {e}'
            return False
    
    async def test_trading_engine_components(self):
        """Test 2: Trading Engine Components Initialization"""
        logger.info("🧪 TEST 2: Trading Engine Components Initialization")

        try:
            # Initialize trading engine components
            success = await self.bridge._initialize_trading_engine()
            assert success, "Trading engine initialization should succeed"

            # Verify components
            assert self.bridge.strategy is not None, "Strategy should be initialized"
            assert hasattr(self.bridge, 'clock_running'), "Clock should be available"
            assert len(self.bridge.connectors) > 0, "Connectors should be available"
            assert "binance_paper_trade" in self.bridge.connectors, "Paper trade connector should be available"

            logger.info("✅ TEST 2 PASSED: Trading engine components initialized successfully")
            self.test_results['trading_engine_components'] = 'PASSED'
            return True

        except Exception as e:
            logger.error(f"❌ TEST 2 FAILED: {e}")
            self.test_results['trading_engine_components'] = f'FAILED: {e}'
            return False
    
    async def test_signal_bus_connection(self):
        """Test 3: Redis Signal Bus Connection"""
        logger.info("🧪 TEST 3: Redis Signal Bus Connection")
        
        try:
            # Test signal bus connection
            success = await self.bridge._connect_to_signal_bus()
            assert success, "Signal bus connection should succeed"
            
            logger.info("✅ TEST 3 PASSED: Signal bus connection successful")
            self.test_results['signal_bus_connection'] = 'PASSED'
            return True
            
        except Exception as e:
            logger.error(f"❌ TEST 3 FAILED: {e}")
            self.test_results['signal_bus_connection'] = f'FAILED: {e}'
            return False
    
    async def test_signal_processing(self):
        """Test 4: Signal Processing and Trade Execution"""
        logger.info("🧪 TEST 4: Signal Processing and Trade Execution")
        
        try:
            # Create test trading signal
            test_signal = {
                'action': 'BUY',
                'pair': 'SOL-USDT',
                'amount': 1.0,
                'price': None,  # Market order
                'exchange': 'binance_paper_trade',
                'timestamp': time.time(),
                'source': 'test_suite'
            }
            
            # Send signal to bridge
            await self.bridge._handle_trading_signal(test_signal)
            
            # Verify signal was queued
            assert len(self.bridge._pending_signals) > 0, "Signal should be queued for processing"
            
            # Verify signal content
            queued_signal = self.bridge._pending_signals[0]
            assert queued_signal['action'] == 'BUY', "Signal action should be preserved"
            assert queued_signal['pair'] == 'SOL-USDT', "Signal pair should be preserved"
            assert queued_signal['amount'] == 1.0, "Signal amount should be preserved"
            
            logger.info("✅ TEST 4 PASSED: Signal processing successful")
            self.test_results['signal_processing'] = 'PASSED'
            return True
            
        except Exception as e:
            logger.error(f"❌ TEST 4 FAILED: {e}")
            self.test_results['signal_processing'] = f'FAILED: {e}'
            return False
    
    async def test_strategy_execution(self):
        """Test 5: Strategy Execution Simulation"""
        logger.info("🧪 TEST 5: Strategy Execution Simulation")
        
        try:
            # Ensure strategy is ready
            strategy = self.bridge.strategy
            assert strategy is not None, "Strategy should be available"
            
            # Simulate strategy tick with pending signal
            if self.bridge._pending_signals:
                # Process one tick
                strategy.on_tick()
                
                # Verify signal was processed (queue should be empty or reduced)
                logger.info(f"📊 Pending signals after tick: {len(self.bridge._pending_signals)}")
            
            logger.info("✅ TEST 5 PASSED: Strategy execution simulation successful")
            self.test_results['strategy_execution'] = 'PASSED'
            return True
            
        except Exception as e:
            logger.error(f"❌ TEST 5 FAILED: {e}")
            self.test_results['strategy_execution'] = f'FAILED: {e}'
            return False
    
    async def test_performance_comparison(self):
        """Test 6: Performance Comparison (Direct vs Docker)"""
        logger.info("🧪 TEST 6: Performance Comparison")
        
        try:
            # Measure signal processing latency
            start_time = time.time()
            
            test_signal = {
                'action': 'SELL',
                'pair': 'BTC-USDT',
                'amount': 0.1,
                'exchange': 'binance_paper_trade',
                'timestamp': time.time()
            }
            
            await self.bridge._handle_trading_signal(test_signal)
            
            processing_latency = time.time() - start_time
            
            # Direct integration should be very fast (< 10ms)
            assert processing_latency < 0.01, f"Processing should be fast, got {processing_latency:.4f}s"
            
            logger.info(f"⚡ Signal processing latency: {processing_latency:.4f}s")
            logger.info("✅ TEST 6 PASSED: Performance validation successful")
            self.test_results['performance_comparison'] = f'PASSED: {processing_latency:.4f}s latency'
            return True
            
        except Exception as e:
            logger.error(f"❌ TEST 6 FAILED: {e}")
            self.test_results['performance_comparison'] = f'FAILED: {e}'
            return False
    
    async def run_all_tests(self):
        """Run complete test suite"""
        logger.info("🚀 Starting Hummingbot Direct Integration Test Suite")
        logger.info("=" * 60)
        
        # Setup
        if not await self.setup():
            logger.error("❌ Test setup failed, aborting")
            return False
        
        # Run tests in sequence
        tests = [
            self.test_bridge_initialization,
            self.test_trading_engine_components,
            self.test_signal_bus_connection,
            self.test_signal_processing,
            self.test_strategy_execution,
            self.test_performance_comparison
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test in tests:
            try:
                if await test():
                    passed_tests += 1
                await asyncio.sleep(0.5)  # Brief pause between tests
            except Exception as e:
                logger.error(f"❌ Test execution error: {e}")
        
        # Final results
        logger.info("=" * 60)
        logger.info("🏁 TEST SUITE COMPLETE")
        logger.info(f"📊 Results: {passed_tests}/{total_tests} tests passed")
        
        for test_name, result in self.test_results.items():
            status = "✅" if result == 'PASSED' or result.startswith('PASSED:') else "❌"
            logger.info(f"{status} {test_name}: {result}")
        
        if passed_tests == total_tests:
            logger.info("🎉 ALL TESTS PASSED - DIRECT INTEGRATION SUCCESSFUL")
            return True
        else:
            logger.error(f"❌ {total_tests - passed_tests} TESTS FAILED")
            return False
    
    async def cleanup(self):
        """Cleanup test environment"""
        if self.bridge:
            await self.bridge.shutdown()


async def main():
    """Main test execution"""
    test_suite = TestHummingbotDirectIntegration()
    
    try:
        success = await test_suite.run_all_tests()
        return success
    finally:
        await test_suite.cleanup()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
