#!/usr/bin/env python3
"""
🔗 NEXUS INTEGRATION SCRIPT FOR HUMMINGBOT
Runs inside Hummingbot container to enable NEXUS Redis Bus communication

ARCHITECTURE:
NEXUS Bridge → Command File → This Script → Hummingbot Core → Exchanges
            ← Response File ←           ← Market Data ←

CAPABILITIES:
- Monitors nexus_commands.json for trading commands
- Executes orders through Hummingbot's internal APIs
- Reports execution results to nexus_responses.json
- Streams market data to nexus_market_data.json
- Handles 50+ exchange connectors through Hummingbot

INTEGRATION POINTS:
- ScriptStrategyBase: Modern Hummingbot script interface
- Market connectors: Direct access to exchange APIs
- Event system: Order completion and market data events
"""

import json
import time
import asyncio
import logging
import fcntl
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from decimal import Decimal

from hummingbot.strategy.script_strategy_base import ScriptStrategyBase
from hummingbot.connector.connector_base import ConnectorBase
from hummingbot.core.data_type.common import OrderType, TradeType
from hummingbot.core.event.events import (
    BuyOrderCompletedEvent,
    SellOrderCompletedEvent,
    OrderFilledEvent,
    MarketEvent
)

class NexusIntegrationScript(ScriptStrategyBase):
    """
    NEXUS Integration Script for Hummingbot
    Enables bidirectional communication with NEXUS Enterprise Service Bus
    """
    
    def __init__(self, connectors: Dict[str, ConnectorBase]):
        super().__init__(connectors)
        
        # File paths for communication with NEXUS Bridge
        self.command_file = Path("/home/<USER>/nexus_commands.json")
        self.response_file = Path("/home/<USER>/nexus_responses.json")
        self.response_jsonl_file = Path("/home/<USER>/nexus_responses.jsonl")
        self.market_data_file = Path("/home/<USER>/nexus_market_data.json")
        
        # State tracking
        self.last_command_check = 0
        self.active_orders: Dict[str, Dict] = {}
        self.market_data_cache: Dict[str, Dict] = {}
        
        # Default connector (can be overridden by commands)
        self.default_connector = "binance"
        
        self.logger().info("🔗 NEXUS Integration Script initialized")
        self.logger().info(f"📁 Command file: {self.command_file}")
        self.logger().info(f"📁 Response file: {self.response_file}")
        self.logger().info(f"📁 Response JSONL file: {self.response_jsonl_file}")
        self.logger().info(f"📁 Market data file: {self.market_data_file}")

        # Initialize communication files
        self._init_communication_files()

        # Signal that script is ready for bridge health check
        self._signal_ready()
    
    def _init_communication_files(self):
        """Initialize communication files with atomic operations"""
        try:
            # Create empty files if they don't exist
            for file_path in [self.command_file, self.response_file, self.response_jsonl_file, self.market_data_file]:
                if not file_path.exists():
                    file_path.touch()
                    self.logger().info(f"📄 Created communication file: {file_path}")

            # Initialize response file with status
            self._atomic_write_json(self.response_file, {
                'type': 'status',
                'status': 'initialized',
                'timestamp': time.time(),
                'message': 'NEXUS Integration Script ready'
            })

        except Exception as e:
            self.logger().error(f"❌ File initialization error: {e}")

    def _signal_ready(self):
        """Signal to NEXUS Bridge that script is ready and operational"""
        try:
            ready_signal = {
                "type": "status",
                "status": "ready",
                "timestamp": time.time(),
                "message": "Hummingbot Integration Script fully initialized and ready"
            }

            # Write ready signal to JSONL file for bridge health check
            self._append_to_jsonl(self.response_jsonl_file, ready_signal)

            self.logger().info("✅ Ready signal sent to NEXUS Bridge")

        except Exception as e:
            self.logger().error(f"❌ Ready signal error: {e}")

    def _append_to_jsonl(self, file_path: Path, data: Dict[str, Any]):
        """Append JSON line to JSONL file with atomic operations"""
        try:
            with open(file_path, 'a') as f:
                fcntl.flock(f.fileno(), fcntl.LOCK_EX)
                json.dump(data, f)
                f.write('\n')
                f.flush()
                os.fsync(f.fileno())

        except Exception as e:
            self.logger().error(f"❌ JSONL append error: {e}")
            raise e

    def _atomic_write_json(self, file_path: Path, data: Dict[str, Any]):
        """Atomic write to prevent file corruption"""
        temp_file = file_path.with_suffix('.tmp')
        try:
            with open(temp_file, 'w') as f:
                fcntl.flock(f.fileno(), fcntl.LOCK_EX)
                json.dump(data, f, indent=2)
                f.flush()
                os.fsync(f.fileno())

            # Atomic move
            temp_file.replace(file_path)

        except Exception as e:
            if temp_file.exists():
                temp_file.unlink()
            raise e

    def _atomic_read_json(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Atomic read with file locking"""
        if not file_path.exists():
            return None

        try:
            with open(file_path, 'r') as f:
                fcntl.flock(f.fileno(), fcntl.LOCK_SH)
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return None
        except Exception as e:
            self.logger().error(f"❌ File read error: {e}")
            return None
    
    def on_tick(self):
        """Called every tick - main execution loop"""
        try:
            # Check for new commands from NEXUS
            self._check_for_commands()
            
            # Update market data
            self._update_market_data()
            
            # Monitor active orders
            self._monitor_active_orders()
            
        except Exception as e:
            self.logger().error(f"❌ Tick error: {e}")
    
    def _check_for_commands(self):
        """Check for new commands from NEXUS Bridge with atomic read"""
        try:
            if not self.command_file.exists():
                return

            # Check if file was modified since last check
            current_modified = self.command_file.stat().st_mtime
            if current_modified <= self.last_command_check:
                return

            self.last_command_check = current_modified

            # Atomic read command
            command_data = self._atomic_read_json(self.command_file)

            if not command_data:
                return

            self.logger().info(f"📨 Received command: {command_data}")

            # Process command
            self._process_command(command_data)

            # Clear command file after processing
            self._atomic_write_json(self.command_file, {})

        except Exception as e:
            self.logger().error(f"❌ Command check error: {e}")
    
    def _process_command(self, command_data: Dict[str, Any]):
        """Process command from NEXUS"""
        try:
            command_id = command_data.get('command_id', 'unknown')
            action = command_data.get('action', '').upper()
            
            if action == 'BUY':
                self._execute_buy_order(command_data)
            elif action == 'SELL':
                self._execute_sell_order(command_data)
            elif action == 'CANCEL':
                self._cancel_order(command_data)
            elif action == 'STATUS':
                self._send_status(command_data)
            elif action == 'BALANCE':
                self._send_balance(command_data)
            else:
                self.logger().warning(f"⚠️ Unknown command: {action}")
                self._atomic_write_json(self.response_file, {
                    'command_id': command_id,
                    'status': 'error',
                    'message': f'Unknown command: {action}',
                    'timestamp': time.time()
                })

        except Exception as e:
            self.logger().error(f"❌ Command processing error: {e}")
            self._atomic_write_json(self.response_file, {
                'command_id': command_data.get('command_id', 'unknown'),
                'status': 'error',
                'message': str(e),
                'timestamp': time.time()
            })
    
    def _execute_buy_order(self, command_data: Dict[str, Any]):
        """Execute buy order"""
        try:
            pair = command_data.get('pair', 'BTC-USDT')
            amount = Decimal(str(command_data.get('amount', 0.01)))
            price = command_data.get('price')  # None for market orders
            exchange = command_data.get('exchange', self.default_connector)
            order_type = command_data.get('order_type', 'market').lower()
            
            # Get connector
            connector = self.connectors.get(exchange)
            if not connector:
                raise Exception(f"Connector not found: {exchange}")
            
            # Parse trading pair
            base, quote = pair.split('-')
            trading_pair = f"{base}-{quote}"
            
            # Determine order type
            hb_order_type = OrderType.MARKET if order_type == 'market' else OrderType.LIMIT
            
            # Place order using Hummingbot's order placement methods
            if hb_order_type == OrderType.MARKET:
                order_id = connector.buy(
                    trading_pair=trading_pair,
                    amount=amount,
                    order_type=OrderType.MARKET
                )
            else:
                order_id = connector.buy(
                    trading_pair=trading_pair,
                    amount=amount,
                    order_type=OrderType.LIMIT,
                    price=Decimal(str(price))
                )
            
            # Track order
            self.active_orders[order_id] = {
                'command_id': command_data.get('command_id'),
                'action': 'BUY',
                'pair': pair,
                'amount': float(amount),
                'price': price,
                'exchange': exchange,
                'timestamp': time.time(),
                'status': 'pending'
            }
            
            self.logger().info(f"✅ Buy order placed: {order_id} - {amount} {pair}")
            
            # Send immediate response
            self._atomic_write_json(self.response_file, {
                'command_id': command_data.get('command_id'),
                'order_id': order_id,
                'status': 'placed',
                'action': 'BUY',
                'pair': pair,
                'amount': float(amount),
                'price': price,
                'exchange': exchange,
                'timestamp': time.time()
            })
            
        except Exception as e:
            self.logger().error(f"❌ Buy order error: {e}")
            self._atomic_write_json(self.response_file, {
                'command_id': command_data.get('command_id'),
                'status': 'error',
                'message': str(e),
                'timestamp': time.time()
            })
    
    def _execute_sell_order(self, command_data: Dict[str, Any]):
        """Execute sell order"""
        try:
            pair = command_data.get('pair', 'BTC-USDT')
            amount = Decimal(str(command_data.get('amount', 0.01)))
            price = command_data.get('price')  # None for market orders
            exchange = command_data.get('exchange', self.default_connector)
            order_type = command_data.get('order_type', 'market').lower()
            
            # Get connector
            connector = self.connectors.get(exchange)
            if not connector:
                raise Exception(f"Connector not found: {exchange}")
            
            # Parse trading pair
            base, quote = pair.split('-')
            trading_pair = f"{base}-{quote}"
            
            # Determine order type
            hb_order_type = OrderType.MARKET if order_type == 'market' else OrderType.LIMIT
            
            # Place order using Hummingbot's order placement methods
            if hb_order_type == OrderType.MARKET:
                order_id = connector.sell(
                    trading_pair=trading_pair,
                    amount=amount,
                    order_type=OrderType.MARKET
                )
            else:
                order_id = connector.sell(
                    trading_pair=trading_pair,
                    amount=amount,
                    order_type=OrderType.LIMIT,
                    price=Decimal(str(price))
                )
            
            # Track order
            self.active_orders[order_id] = {
                'command_id': command_data.get('command_id'),
                'action': 'SELL',
                'pair': pair,
                'amount': float(amount),
                'price': price,
                'exchange': exchange,
                'timestamp': time.time(),
                'status': 'pending'
            }
            
            self.logger().info(f"✅ Sell order placed: {order_id} - {amount} {pair}")
            
            # Send immediate response
            self._atomic_write_json(self.response_file, {
                'command_id': command_data.get('command_id'),
                'order_id': order_id,
                'status': 'placed',
                'action': 'SELL',
                'pair': pair,
                'amount': float(amount),
                'price': price,
                'exchange': exchange,
                'timestamp': time.time()
            })

        except Exception as e:
            self.logger().error(f"❌ Sell order error: {e}")
            self._atomic_write_json(self.response_file, {
                'command_id': command_data.get('command_id'),
                'status': 'error',
                'message': str(e),
                'timestamp': time.time()
            })
    
    def _cancel_order(self, command_data: Dict[str, Any]):
        """Cancel order"""
        try:
            order_id = command_data.get('order_id')
            exchange = command_data.get('exchange', self.default_connector)
            
            # Get connector
            connector = self.connectors.get(exchange)
            if not connector:
                raise Exception(f"Connector not found: {exchange}")
            
            # Cancel order
            connector.cancel(order_id)
            
            # Update tracking
            if order_id in self.active_orders:
                self.active_orders[order_id]['status'] = 'cancelled'
            
            self.logger().info(f"🚫 Order cancelled: {order_id}")
            
            # Send response
            self._atomic_write_json(self.response_file, {
                'command_id': command_data.get('command_id'),
                'order_id': order_id,
                'status': 'cancelled',
                'timestamp': time.time()
            })

        except Exception as e:
            self.logger().error(f"❌ Cancel order error: {e}")
            self._atomic_write_json(self.response_file, {
                'command_id': command_data.get('command_id'),
                'status': 'error',
                'message': str(e),
                'timestamp': time.time()
            })
    
    def _send_status(self, command_data: Dict[str, Any]):
        """Send status information"""
        try:
            status = {
                'command_id': command_data.get('command_id'),
                'type': 'status',
                'status': 'healthy',
                'active_orders': len(self.active_orders),
                'connectors': list(self.connectors.keys()),
                'uptime': time.time() - self.start_time if hasattr(self, 'start_time') else 0,
                'timestamp': time.time()
            }
            
            self._atomic_write_json(self.response_file, status)

        except Exception as e:
            self.logger().error(f"❌ Status error: {e}")

    def _send_balance(self, command_data: Dict[str, Any]):
        """Send balance information"""
        try:
            exchange = command_data.get('exchange', self.default_connector)
            connector = self.connectors.get(exchange)

            if not connector:
                raise Exception(f"Connector not found: {exchange}")

            # Get balances
            balances = {}
            for token, balance in connector.get_all_balances().items():
                balances[token] = float(balance)

            response = {
                'command_id': command_data.get('command_id'),
                'type': 'balance',
                'exchange': exchange,
                'balances': balances,
                'timestamp': time.time()
            }

            self._atomic_write_json(self.response_file, response)
            
        except Exception as e:
            self.logger().error(f"❌ Balance error: {e}")
    
    def _monitor_active_orders(self):
        """Monitor active orders for completion"""
        # This will be called by Hummingbot's event system
        # Order completion events are handled by did_complete_buy_order/did_complete_sell_order
        pass
    
    def _update_market_data(self):
        """Update market data cache and file"""
        try:
            market_data = {}
            
            for connector_name, connector in self.connectors.items():
                if not connector.ready:
                    continue
                
                connector_data = {}
                
                # Get order book data for active trading pairs
                for trading_pair in connector.trading_pairs:
                    try:
                        order_book = connector.get_order_book(trading_pair)
                        if order_book:
                            connector_data[trading_pair] = {
                                'bid': float(order_book.get_price(False)),  # Best bid
                                'ask': float(order_book.get_price(True)),   # Best ask
                                'mid': float(order_book.get_mid_price()),
                                'timestamp': time.time()
                            }
                    except Exception:
                        continue
                
                if connector_data:
                    market_data[connector_name] = connector_data
            
            # Write to file
            if market_data:
                with open(self.market_data_file, 'w') as f:
                    json.dump(market_data, f)
            
        except Exception as e:
            self.logger().error(f"❌ Market data update error: {e}")
    
    def did_complete_buy_order(self, event: BuyOrderCompletedEvent):
        """Handle buy order completion"""
        try:
            order_id = event.order_id

            if order_id in self.active_orders:
                order_info = self.active_orders[order_id]
                order_info['status'] = 'filled'

                # Send completion response
                self._atomic_write_json(self.response_file, {
                    'command_id': order_info.get('command_id'),
                    'order_id': order_id,
                    'status': 'filled',
                    'action': 'BUY',
                    'pair': order_info['pair'],
                    'amount': order_info['amount'],
                    'price': float(event.price),
                    'fees': float(event.fee_amount),
                    'exchange': order_info['exchange'],
                    'timestamp': time.time()
                })

                self.logger().info(f"✅ Buy order completed: {order_id}")

                # Remove from active orders
                del self.active_orders[order_id]

        except Exception as e:
            self.logger().error(f"❌ Buy completion error: {e}")

    def did_complete_sell_order(self, event: SellOrderCompletedEvent):
        """Handle sell order completion"""
        try:
            order_id = event.order_id

            if order_id in self.active_orders:
                order_info = self.active_orders[order_id]
                order_info['status'] = 'filled'

                # Send completion response
                self._atomic_write_json(self.response_file, {
                    'command_id': order_info.get('command_id'),
                    'order_id': order_id,
                    'status': 'filled',
                    'action': 'SELL',
                    'pair': order_info['pair'],
                    'amount': order_info['amount'],
                    'price': float(event.price),
                    'fees': float(event.fee_amount),
                    'exchange': order_info['exchange'],
                    'timestamp': time.time()
                })

                self.logger().info(f"✅ Sell order completed: {order_id}")

                # Remove from active orders
                del self.active_orders[order_id]

        except Exception as e:
            self.logger().error(f"❌ Sell completion error: {e}")
